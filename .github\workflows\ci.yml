name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  plugin:
    name: Plugin Build & Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Build plugin
      run: mvn -q -B clean compile
    
    - name: Run tests
      run: mvn -q -B test
    
    - name: Package plugin
      run: mvn -q -B package -DskipTests
    
    - name: Upload plugin artifact
      uses: actions/upload-artifact@v3
      with:
        name: orvynmmo-plugin
        path: plugin/target/OrvynMMO-*-shaded.jar

  editor:
    name: Editor Build & Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: editor/package-lock.json
    
    - name: Install dependencies
      working-directory: editor
      run: npm ci
    
    - name: Type check
      working-directory: editor
      run: npm run typecheck
    
    - name: Lint
      working-directory: editor
      run: npm run lint
    
    - name: Build
      working-directory: editor
      run: npm run build
    
    - name: Upload editor artifact
      uses: actions/upload-artifact@v3
      with:
        name: orvynmmo-editor
        path: editor/dist/

  cli:
    name: CLI Build & Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: cli/package-lock.json
    
    - name: Install dependencies
      working-directory: cli
      run: npm ci
    
    - name: Type check
      working-directory: cli
      run: npm run typecheck
    
    - name: Lint
      working-directory: cli
      run: npm run lint
    
    - name: Build
      working-directory: cli
      run: npm run build
    
    - name: Test
      working-directory: cli
      run: npm test
    
    - name: Upload CLI artifact
      uses: actions/upload-artifact@v3
      with:
        name: orvynmmo-cli
        path: cli/dist/

  schemas:
    name: Schema Validation
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Install AJV CLI
      run: npm install -g ajv-cli
    
    - name: Validate schemas
      run: |
        ajv validate -s schemas/pack.schema.json -d "examples/packs/*/pack.json"
        ajv validate -s schemas/skill.schema.json -d "examples/packs/*/skills/*.json"
