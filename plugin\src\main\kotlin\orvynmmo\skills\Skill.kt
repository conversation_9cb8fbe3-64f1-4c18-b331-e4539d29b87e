package orvynmmo.skills

import orvynmmo.core.id.SkillId
import orvynmmo.skills.ir.SkillIR
import orvynmmo.skills.timeline.Timeline

/**
 * Represents a compiled skill in OrvynMMO.
 * 
 * Skills are immutable objects created from validated IR (Intermediate Representation).
 * They contain all the data needed for deterministic execution.
 */
data class Skill(
    val id: SkillId,
    val displayName: String,
    val description: String,
    val icon: String,
    val cooldown: Long,
    val manaCost: Int,
    val castTime: Long,
    val range: Double,
    val targetType: TargetType,
    val timeline: Timeline,
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * Checks if this skill can be cast by the given caster
     */
    fun canCast(context: SkillContext): Bo<PERSON>an {
        // Check cooldown
        if (context.isOnCooldown(id)) {
            return false
        }
        
        // Check mana
        if (context.caster.mana < manaCost) {
            return false
        }
        
        // Check range if target is required
        if (targetType.requiresTarget && context.target != null) {
            val distance = context.caster.location.distance(context.target.location)
            if (distance > range) {
                return false
            }
        }
        
        return true
    }
    
    /**
     * Gets the effective cooldown for this skill
     */
    fun getEffectiveCooldown(context: SkillContext): Long {
        // Apply cooldown reduction modifiers
        val reduction = context.caster.getCooldownReduction()
        return (cooldown * (1.0 - reduction)).toLong().coerceAtLeast(0)
    }
    
    /**
     * Gets the effective mana cost for this skill
     */
    fun getEffectiveManaCost(context: SkillContext): Int {
        // Apply mana cost modifiers
        val reduction = context.caster.getManaCostReduction()
        return (manaCost * (1.0 - reduction)).toInt().coerceAtLeast(0)
    }
    
    /**
     * Gets the effective cast time for this skill
     */
    fun getEffectiveCastTime(context: SkillContext): Long {
        // Apply cast speed modifiers
        val speedMultiplier = context.caster.getCastSpeed()
        return (castTime / speedMultiplier).toLong().coerceAtLeast(1)
    }
    
    /**
     * Gets the effective range for this skill
     */
    fun getEffectiveRange(context: SkillContext): Double {
        // Apply range modifiers
        val rangeMultiplier = context.caster.getRangeMultiplier()
        return range * rangeMultiplier
    }
}

/**
 * Defines how a skill targets entities
 */
enum class TargetType {
    /**
     * No target required - self-cast or area effect
     */
    NONE,
    
    /**
     * Requires a single entity target
     */
    SINGLE_ENTITY,
    
    /**
     * Requires a location target
     */
    LOCATION,
    
    /**
     * Targets all entities in an area
     */
    AREA,
    
    /**
     * Targets entities in a line/ray
     */
    LINE,
    
    /**
     * Targets entities in a cone
     */
    CONE;
    
    /**
     * Whether this target type requires an explicit target
     */
    val requiresTarget: Boolean
        get() = this in setOf(SINGLE_ENTITY, LOCATION)
}

/**
 * Extension functions for skill caster attributes
 */
fun SkillCaster.getCooldownReduction(): Double {
    return getAttribute("cooldown_reduction") ?: 0.0
}

fun SkillCaster.getManaCostReduction(): Double {
    return getAttribute("mana_cost_reduction") ?: 0.0
}

fun SkillCaster.getCastSpeed(): Double {
    return getAttribute("cast_speed") ?: 1.0
}

fun SkillCaster.getRangeMultiplier(): Double {
    return getAttribute("range_multiplier") ?: 1.0
}
