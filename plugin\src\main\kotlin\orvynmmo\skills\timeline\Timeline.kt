package orvynmmo.skills.timeline

import orvynmmo.skills.SkillContext
import orvynmmo.skills.mechanics.Mechanic

/**
 * Represents a deterministic timeline of skill mechanics.
 * 
 * Timelines define when mechanics execute during skill casting,
 * ensuring consistent and predictable behavior across all executions.
 */
data class Timeline(
    val phases: List<TimelinePhase>,
    val totalDuration: Long,
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * Gets all mechanics that should execute at a specific tick
     */
    fun getMechanicsAtTick(tick: Long): List<TimelineMechanic> {
        return phases.flatMap { phase ->
            phase.getMechanicsAtTick(tick)
        }
    }
    
    /**
     * Gets the phase that is active at a specific tick
     */
    fun getPhaseAtTick(tick: Long): TimelinePhase? {
        return phases.find { phase ->
            tick >= phase.startTick && tick < phase.endTick
        }
    }
    
    /**
     * Checks if the timeline is complete at a specific tick
     */
    fun isComplete(tick: Long): Boolean {
        return tick >= totalDuration
    }
    
    /**
     * Gets all mechanics in execution order
     */
    fun getAllMechanics(): List<TimelineMechanic> {
        return phases.flatMap { it.mechanics }.sortedBy { it.tick }
    }
    
    /**
     * Validates the timeline structure
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        
        // Check phase ordering
        for (i in 1 until phases.size) {
            val prevPhase = phases[i - 1]
            val currentPhase = phases[i]
            
            if (currentPhase.startTick < prevPhase.endTick) {
                errors.add("Phase ${currentPhase.name} overlaps with previous phase")
            }
        }
        
        // Check mechanics are within phase bounds
        phases.forEach { phase ->
            phase.mechanics.forEach { mechanic ->
                if (mechanic.tick < phase.startTick || mechanic.tick >= phase.endTick) {
                    errors.add("Mechanic at tick ${mechanic.tick} is outside phase ${phase.name} bounds")
                }
            }
        }
        
        return errors
    }
}

/**
 * A phase within a timeline
 */
data class TimelinePhase(
    val name: String,
    val startTick: Long,
    val endTick: Long,
    val mechanics: List<TimelineMechanic>,
    val conditions: List<PhaseCondition> = emptyList(),
    val metadata: Map<String, Any> = emptyMap()
) {
    
    val duration: Long
        get() = endTick - startTick
    
    /**
     * Gets mechanics that execute at a specific tick within this phase
     */
    fun getMechanicsAtTick(tick: Long): List<TimelineMechanic> {
        if (tick < startTick || tick >= endTick) {
            return emptyList()
        }
        
        return mechanics.filter { it.tick == tick }
    }
    
    /**
     * Checks if this phase should be active given the context
     */
    fun shouldActivate(context: SkillContext): Boolean {
        return conditions.all { it.evaluate(context) }
    }
}

/**
 * A mechanic scheduled to execute at a specific tick
 */
data class TimelineMechanic(
    val tick: Long,
    val mechanic: Mechanic,
    val conditions: List<MechanicCondition> = emptyList(),
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * Checks if this mechanic should execute given the context
     */
    fun shouldExecute(context: SkillContext): Boolean {
        return conditions.all { it.evaluate(context) }
    }
}

/**
 * Condition that determines if a phase should activate
 */
interface PhaseCondition {
    /**
     * Evaluates the condition
     */
    fun evaluate(context: SkillContext): Boolean
}

/**
 * Condition that determines if a mechanic should execute
 */
interface MechanicCondition {
    /**
     * Evaluates the condition
     */
    fun evaluate(context: SkillContext): Boolean
}

/**
 * Builder for creating timelines
 */
class TimelineBuilder {
    private val phases = mutableListOf<TimelinePhase>()
    private var currentTick = 0L
    
    /**
     * Adds a phase to the timeline
     */
    fun phase(
        name: String,
        duration: Long,
        builder: TimelinePhaseBuilder.() -> Unit
    ): TimelineBuilder {
        val phaseBuilder = TimelinePhaseBuilder(name, currentTick, currentTick + duration)
        phaseBuilder.builder()
        
        phases.add(phaseBuilder.build())
        currentTick += duration
        
        return this
    }
    
    /**
     * Builds the timeline
     */
    fun build(): Timeline {
        return Timeline(
            phases = phases.toList(),
            totalDuration = currentTick
        )
    }
}

/**
 * Builder for creating timeline phases
 */
class TimelinePhaseBuilder(
    private val name: String,
    private val startTick: Long,
    private val endTick: Long
) {
    private val mechanics = mutableListOf<TimelineMechanic>()
    private val conditions = mutableListOf<PhaseCondition>()
    
    /**
     * Adds a mechanic at a specific tick offset within the phase
     */
    fun mechanic(
        tickOffset: Long,
        mechanic: Mechanic,
        conditions: List<MechanicCondition> = emptyList()
    ): TimelinePhaseBuilder {
        val absoluteTick = startTick + tickOffset
        if (absoluteTick >= endTick) {
            throw IllegalArgumentException("Mechanic tick $absoluteTick is outside phase bounds")
        }
        
        mechanics.add(TimelineMechanic(absoluteTick, mechanic, conditions))
        return this
    }
    
    /**
     * Adds a condition to the phase
     */
    fun condition(condition: PhaseCondition): TimelinePhaseBuilder {
        conditions.add(condition)
        return this
    }
    
    /**
     * Builds the phase
     */
    fun build(): TimelinePhase {
        return TimelinePhase(
            name = name,
            startTick = startTick,
            endTick = endTick,
            mechanics = mechanics.toList(),
            conditions = conditions.toList()
        )
    }
}
