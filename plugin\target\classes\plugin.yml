name: OrvynMMO
version: '${project.version}'
main: orvynmmo.OrvynMMOPlugin
api-version: '1.21'
folia-supported: true
description: Next-gen, Folia-safe, cinematic MMO framework for Paper/Purpur
author: OrvynMMO Team
website: https://github.com/orvynmmo/orvynmmo

commands:
  orvyn:
    description: Main OrvynMMO command
    usage: /orvyn <subcommand>
    permission: orvyn.admin
    aliases: [ovn]

permissions:
  orvyn.*:
    description: All OrvynMMO permissions
    children:
      orvyn.admin.*: true
      orvyn.profile: true
      orvyn.reload: true
      orvyn.test: true
      orvyn.import: true
  
  orvyn.admin.*:
    description: All admin permissions
    children:
      orvyn.admin.reload: true
      orvyn.admin.profile: true
      orvyn.admin.test: true
      orvyn.admin.import: true
      orvyn.admin.debug: true
  
  orvyn.admin.reload:
    description: Permission to reload OrvynMMO configuration
    default: op
  
  orvyn.admin.profile:
    description: Permission to use profiling commands
    default: op
  
  orvyn.admin.test:
    description: Permission to run test encounters
    default: op
  
  orvyn.admin.import:
    description: Permission to import from other plugins
    default: op
  
  orvyn.admin.debug:
    description: Permission to use debug features
    default: op
  
  orvyn.profile:
    description: Permission to view performance profiles
    default: op
  
  orvyn.reload:
    description: Permission to reload configurations
    default: op
  
  orvyn.test:
    description: Permission to run test scenarios
    default: op
  
  orvyn.import:
    description: Permission to import content
    default: op
