package orvynmmo.core.registry

import orvynmmo.core.id.*
import orvynmmo.items.OrvynItem
import orvynmmo.rpg.OrvynClass
import orvynmmo.skills.Skill
import java.util.concurrent.atomic.AtomicReference

/**
 * Manages all registries in OrvynMMO.
 * 
 * Provides atomic snapshots of all registries that can be swapped during hot-reload.
 * Thread-safe and designed for high-performance concurrent access.
 */
class RegistryManager {
    
    // Atomic references to current registry snapshots
    private val skillsRef = AtomicReference(Registry.empty<Skill>())
    private val itemsRef = AtomicReference(Registry.empty<OrvynItem>())
    private val classesRef = AtomicReference(Registry.empty<OrvynClass>())
    
    /**
     * Current skills registry snapshot
     */
    val skills: Registry<Skill>
        get() = skillsRef.get()
    
    /**
     * Current items registry snapshot
     */
    val items: Registry<OrvynItem>
        get() = itemsRef.get()
    
    /**
     * Current classes registry snapshot
     */
    val classes: Registry<OrvynClass>
        get() = classesRef.get()
    
    /**
     * Gets a skill by ID
     */
    fun getSkill(id: SkillId): Skill? = skills[id.id]
    
    /**
     * Gets a skill by ID, throwing if not found
     */
    fun getSkillRequired(id: SkillId): Skill = skills.getRequired(id.id)
    
    /**
     * Gets an item by ID
     */
    fun getItem(id: ItemId): OrvynItem? = items[id.id]
    
    /**
     * Gets an item by ID, throwing if not found
     */
    fun getItemRequired(id: ItemId): OrvynItem = items.getRequired(id.id)
    
    /**
     * Gets a class by ID
     */
    fun getClass(id: ClassId): OrvynClass? = classes[id.id]
    
    /**
     * Gets a class by ID, throwing if not found
     */
    fun getClassRequired(id: ClassId): OrvynClass = classes.getRequired(id.id)
    
    /**
     * Creates a snapshot of all current registries
     */
    fun createSnapshot(): RegistrySnapshot {
        return RegistrySnapshot(
            skills = skills,
            items = items,
            classes = classes
        )
    }
    
    /**
     * Atomically updates all registries from a snapshot
     */
    fun updateFromSnapshot(snapshot: RegistrySnapshot) {
        skillsRef.set(snapshot.skills)
        itemsRef.set(snapshot.items)
        classesRef.set(snapshot.classes)
    }
    
    /**
     * Updates the skills registry
     */
    fun updateSkills(newRegistry: Registry<Skill>) {
        skillsRef.set(newRegistry)
    }
    
    /**
     * Updates the items registry
     */
    fun updateItems(newRegistry: Registry<OrvynItem>) {
        itemsRef.set(newRegistry)
    }
    
    /**
     * Updates the classes registry
     */
    fun updateClasses(newRegistry: Registry<OrvynClass>) {
        classesRef.set(newRegistry)
    }
    
    /**
     * Clears all registries
     */
    fun clear() {
        skillsRef.set(Registry.empty())
        itemsRef.set(Registry.empty())
        classesRef.set(Registry.empty())
    }
    
    /**
     * Gets statistics about all registries
     */
    fun getStats(): RegistryStats {
        return RegistryStats(
            skillCount = skills.size,
            itemCount = items.size,
            classCount = classes.size
        )
    }
}

/**
 * Immutable snapshot of all registries
 */
data class RegistrySnapshot(
    val skills: Registry<Skill>,
    val items: Registry<OrvynItem>,
    val classes: Registry<OrvynClass>
) {
    
    /**
     * Creates a diff between this snapshot and another
     */
    fun diff(other: RegistrySnapshot): SnapshotDiff {
        return SnapshotDiff(
            skills = skills.diff(other.skills),
            items = items.diff(other.items),
            classes = classes.diff(other.classes)
        )
    }
}

/**
 * Represents the difference between two registry snapshots
 */
data class SnapshotDiff(
    val skills: RegistryDiff<Skill>,
    val items: RegistryDiff<OrvynItem>,
    val classes: RegistryDiff<OrvynClass>
) {
    
    /**
     * Checks if there are any changes across all registries
     */
    val hasChanges: Boolean
        get() = skills.hasChanges || items.hasChanges || classes.hasChanges
    
    /**
     * Gets the total number of changes across all registries
     */
    val totalChanges: Int
        get() = skills.changeCount + items.changeCount + classes.changeCount
    
    /**
     * Creates a summary of all changes
     */
    fun summary(): String {
        val parts = mutableListOf<String>()
        
        if (skills.hasChanges) {
            parts.add("Skills: ${skills.summary()}")
        }
        if (items.hasChanges) {
            parts.add("Items: ${items.summary()}")
        }
        if (classes.hasChanges) {
            parts.add("Classes: ${classes.summary()}")
        }
        
        return if (parts.isEmpty()) {
            "No changes"
        } else {
            parts.joinToString("; ")
        }
    }
}

/**
 * Statistics about registry contents
 */
data class RegistryStats(
    val skillCount: Int,
    val itemCount: Int,
    val classCount: Int
) {
    
    val totalEntries: Int
        get() = skillCount + itemCount + classCount
    
    override fun toString(): String {
        return "RegistryStats(skills=$skillCount, items=$itemCount, classes=$classCount, total=$totalEntries)"
    }
}
