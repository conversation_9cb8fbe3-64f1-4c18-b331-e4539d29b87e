{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://orvynmmo.com/schemas/skill.schema.json", "title": "<PERSON><PERSON>", "description": "Schema for OrvynMMO skill definitions", "type": "object", "required": ["id", "displayName", "targetType", "mechanics"], "properties": {"id": {"type": "string", "pattern": "^[a-z0-9_.-]+:[a-z0-9_./\\-]+$", "description": "Unique skill identifier (namespace:key format)"}, "displayName": {"type": "string", "maxLength": 100, "description": "Display name shown to players"}, "description": {"type": "string", "maxLength": 1000, "description": "Skill description"}, "icon": {"type": "string", "description": "Icon identifier or path"}, "cooldown": {"type": "integer", "minimum": 0, "description": "Cooldown in milliseconds"}, "manaCost": {"type": "integer", "minimum": 0, "description": "<PERSON>a cost to cast"}, "castTime": {"type": "integer", "minimum": 0, "description": "Cast time in milliseconds"}, "range": {"type": "number", "minimum": 0, "description": "Maximum range in blocks"}, "targetType": {"type": "string", "enum": ["none", "single_entity", "location", "area", "line", "cone"], "description": "How the skill targets entities"}, "mechanics": {"type": "array", "items": {"$ref": "#/definitions/mechanic"}, "description": "List of mechanics that make up this skill"}, "conditions": {"type": "array", "items": {"$ref": "#/definitions/condition"}, "description": "Conditions that must be met to cast this skill"}, "metadata": {"type": "object", "description": "Additional metadata"}}, "definitions": {"mechanic": {"type": "object", "required": ["type", "tick"], "properties": {"type": {"type": "string", "description": "Mechanic type identifier"}, "tick": {"type": "integer", "minimum": 0, "description": "Tick when this mechanic executes"}, "parameters": {"type": "object", "description": "Mechanic-specific parameters"}, "conditions": {"type": "array", "items": {"$ref": "#/definitions/condition"}, "description": "Conditions for this mechanic"}}}, "condition": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "Condition type identifier"}, "parameters": {"type": "object", "description": "Condition-specific parameters"}}}}}