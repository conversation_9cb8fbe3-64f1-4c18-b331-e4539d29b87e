package orvynmmo.core.id

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class OrvynIdTest {
    
    @Test
    fun `valid ID creation`() {
        val id = OrvynId("orvyn:test_skill")
        assertEquals("orvyn", id.namespace)
        assertEquals("test_skill", id.key)
        assertEquals("orvyn:test_skill", id.value)
    }
    
    @Test
    fun `orvyn namespace helper`() {
        val id = OrvynId.orvyn("test_skill")
        assertEquals("orvyn", id.namespace)
        assertEquals("test_skill", id.key)
        assertTrue(id.isOrvynNamespace)
    }
    
    @Test
    fun `custom namespace`() {
        val id = OrvynId.of("custom", "test_skill")
        assertEquals("custom", id.namespace)
        assertEquals("test_skill", id.key)
        assertFalse(id.isOrvynNamespace)
    }
    
    @Test
    fun `parse with namespace`() {
        val id = OrvynId.parse("custom:test_skill")
        assertEquals("custom", id.namespace)
        assertEquals("test_skill", id.key)
    }
    
    @Test
    fun `parse without namespace defaults to orvyn`() {
        val id = OrvynId.parse("test_skill")
        assertEquals("orvyn", id.namespace)
        assertEquals("test_skill", id.key)
    }
    
    @Test
    fun `invalid ID format throws exception`() {
        assertThrows<IllegalArgumentException> {
            OrvynId("invalid_format")
        }
        
        assertThrows<IllegalArgumentException> {
            OrvynId("UPPERCASE:not_allowed")
        }
        
        assertThrows<IllegalArgumentException> {
            OrvynId("namespace:")
        }
        
        assertThrows<IllegalArgumentException> {
            OrvynId(":key")
        }
    }
    
    @Test
    fun `ID validation`() {
        assertTrue(OrvynId.isValid("orvyn:test_skill"))
        assertTrue(OrvynId.isValid("custom:test.skill"))
        assertTrue(OrvynId.isValid("mod:path/to/skill"))
        assertTrue(OrvynId.isValid("test:skill-name"))
        
        assertFalse(OrvynId.isValid("invalid"))
        assertFalse(OrvynId.isValid("UPPERCASE:skill"))
        assertFalse(OrvynId.isValid("namespace:"))
        assertFalse(OrvynId.isValid(":key"))
        assertFalse(OrvynId.isValid(""))
    }
    
    @Test
    fun `typed ID creation`() {
        val skillId = SkillId.orvyn("fireball")
        assertEquals("orvyn", skillId.namespace)
        assertEquals("fireball", skillId.key)
        assertEquals("orvyn:fireball", skillId.toString())
        
        val itemId = ItemId.of("custom", "sword")
        assertEquals("custom", itemId.namespace)
        assertEquals("sword", itemId.key)
        
        val classId = ClassId.parse("warrior")
        assertEquals("orvyn", classId.namespace)
        assertEquals("warrior", classId.key)
    }
}
