package orvynmmo.region

import kotlinx.coroutines.*
import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.plugin.Plugin
import java.util.concurrent.CompletableFuture
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * Thread-safe gate for executing operations on the correct region thread.
 * 
 * Automatically handles Folia region scheduling or falls back to main thread
 * scheduling on Paper/Spigot.
 */
class RegionGate(
    private val plugin: Plugin,
    private val location: Location,
    private val isFolia: Boolean
) {
    
    private val isShutdown = AtomicBoolean(false)
    private val taskCount = AtomicLong(0)
    private val lastActivity = AtomicLong(System.currentTimeMillis())
    
    /**
     * Checks if this gate is still active
     */
    val isActive: Boolean
        get() = !isShutdown.get() && (System.currentTimeMillis() - lastActivity.get()) < INACTIVE_THRESHOLD
    
    /**
     * Gets the number of tasks executed through this gate
     */
    val executedTasks: Long
        get() = taskCount.get()
    
    /**
     * Executes a task on the appropriate thread
     */
    fun execute(task: Runnable) {
        if (isShutdown.get()) {
            throw IllegalStateException("RegionGate is shutdown")
        }
        
        lastActivity.set(System.currentTimeMillis())
        taskCount.incrementAndGet()
        
        if (isFolia) {
            executeFolia(task)
        } else {
            executePaper(task)
        }
    }
    
    /**
     * Executes a task on the appropriate thread with a result
     */
    fun <T> execute(task: () -> T): CompletableFuture<T> {
        if (isShutdown.get()) {
            return CompletableFuture.failedFuture(IllegalStateException("RegionGate is shutdown"))
        }
        
        lastActivity.set(System.currentTimeMillis())
        taskCount.incrementAndGet()
        
        val future = CompletableFuture<T>()
        
        val wrappedTask = Runnable {
            try {
                val result = task()
                future.complete(result)
            } catch (e: Exception) {
                future.completeExceptionally(e)
            }
        }
        
        if (isFolia) {
            executeFolia(wrappedTask)
        } else {
            executePaper(wrappedTask)
        }
        
        return future
    }
    
    /**
     * Executes a suspending function on the appropriate thread
     */
    suspend fun <T> executeSuspending(task: suspend () -> T): T {
        return withContext(Dispatchers.Default) {
            val future = execute { runBlocking { task() } }
            future.get()
        }
    }
    
    /**
     * Schedules a delayed task
     */
    fun executeDelayed(delayTicks: Long, task: Runnable) {
        if (isShutdown.get()) {
            throw IllegalStateException("RegionGate is shutdown")
        }
        
        if (isFolia) {
            executeDelayedFolia(delayTicks, task)
        } else {
            executeDelayedPaper(delayTicks, task)
        }
    }
    
    /**
     * Schedules a repeating task
     */
    fun executeRepeating(delayTicks: Long, periodTicks: Long, task: Runnable): RegionTask {
        if (isShutdown.get()) {
            throw IllegalStateException("RegionGate is shutdown")
        }
        
        return if (isFolia) {
            executeRepeatingFolia(delayTicks, periodTicks, task)
        } else {
            executeRepeatingPaper(delayTicks, periodTicks, task)
        }
    }
    
    /**
     * Shuts down this region gate
     */
    fun shutdown() {
        isShutdown.set(true)
    }
    
    private fun executeFolia(task: Runnable) {
        try {
            // Use reflection to call Folia's region scheduler
            val regionizedServer = Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
            val getRegionScheduler = regionizedServer.getMethod("getRegionScheduler")
            val regionScheduler = getRegionScheduler.invoke(null)
            
            val executeMethod = regionScheduler.javaClass.getMethod(
                "execute", 
                Plugin::class.java, 
                Location::class.java, 
                Runnable::class.java
            )
            
            executeMethod.invoke(regionScheduler, plugin, location, task)
        } catch (e: Exception) {
            // Fallback to main thread if Folia reflection fails
            executePaper(task)
        }
    }
    
    private fun executePaper(task: Runnable) {
        if (Bukkit.isPrimaryThread()) {
            task.run()
        } else {
            Bukkit.getScheduler().runTask(plugin, task)
        }
    }
    
    private fun executeDelayedFolia(delayTicks: Long, task: Runnable) {
        try {
            val regionizedServer = Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
            val getRegionScheduler = regionizedServer.getMethod("getRegionScheduler")
            val regionScheduler = getRegionScheduler.invoke(null)
            
            val runDelayedMethod = regionScheduler.javaClass.getMethod(
                "runDelayed",
                Plugin::class.java,
                Location::class.java,
                Runnable::class.java,
                Long::class.javaPrimitiveType
            )
            
            runDelayedMethod.invoke(regionScheduler, plugin, location, task, delayTicks)
        } catch (e: Exception) {
            executeDelayedPaper(delayTicks, task)
        }
    }
    
    private fun executeDelayedPaper(delayTicks: Long, task: Runnable) {
        Bukkit.getScheduler().runTaskLater(plugin, task, delayTicks)
    }
    
    private fun executeRepeatingFolia(delayTicks: Long, periodTicks: Long, task: Runnable): RegionTask {
        return try {
            val regionizedServer = Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
            val getRegionScheduler = regionizedServer.getMethod("getRegionScheduler")
            val regionScheduler = getRegionScheduler.invoke(null)
            
            val runAtFixedRateMethod = regionScheduler.javaClass.getMethod(
                "runAtFixedRate",
                Plugin::class.java,
                Location::class.java,
                Runnable::class.java,
                Long::class.javaPrimitiveType,
                Long::class.javaPrimitiveType
            )
            
            val scheduledTask = runAtFixedRateMethod.invoke(
                regionScheduler, plugin, location, task, delayTicks, periodTicks
            )
            
            FoliaRegionTask(scheduledTask)
        } catch (e: Exception) {
            executeRepeatingPaper(delayTicks, periodTicks, task)
        }
    }
    
    private fun executeRepeatingPaper(delayTicks: Long, periodTicks: Long, task: Runnable): RegionTask {
        val bukkitTask = Bukkit.getScheduler().runTaskTimer(plugin, task, delayTicks, periodTicks)
        return BukkitRegionTask(bukkitTask)
    }
    
    companion object {
        private const val INACTIVE_THRESHOLD = 300_000L // 5 minutes
    }
}
