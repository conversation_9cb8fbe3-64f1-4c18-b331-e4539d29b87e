#!/usr/bin/env node

import { Command } from 'commander'
import chalk from 'chalk'
import { packCommand } from './commands/pack.js'
import { importCommand } from './commands/import.js'
import { testCommand } from './commands/test.js'
import { doctorCommand } from './commands/doctor.js'

const program = new Command()

program
  .name('orvyn')
  .description('OrvynMMO CLI - Next-gen MMO framework toolkit')
  .version('1.0.0')

// Add commands
program.addCommand(packCommand)
program.addCommand(importCommand)
program.addCommand(testCommand)
program.addCommand(doctorCommand)

// Global error handler
program.exitOverride((err) => {
  if (err.code === 'commander.help') {
    process.exit(0)
  }
  if (err.code === 'commander.version') {
    process.exit(0)
  }
  console.error(chalk.red('Error:'), err.message)
  process.exit(1)
})

// Parse arguments
program.parse()

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp()
}
