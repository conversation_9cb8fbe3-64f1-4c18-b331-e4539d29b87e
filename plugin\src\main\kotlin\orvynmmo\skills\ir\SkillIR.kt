package orvynmmo.skills.ir

/**
 * Intermediate Representation for skills.
 * 
 * This is the compiled form of skills that gets loaded from JSON/HOCON.
 */
data class SkillIR(
    val id: String,
    val displayName: String,
    val description: String,
    val icon: String,
    val cooldown: Long,
    val manaCost: Int,
    val castTime: Long,
    val range: Double,
    val targetType: String,
    val mechanics: List<MechanicIR>,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * Intermediate Representation for mechanics.
 */
data class MechanicIR(
    val type: String,
    val tick: Long,
    val parameters: Map<String, Any> = emptyMap()
)
