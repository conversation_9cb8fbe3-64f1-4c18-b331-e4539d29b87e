{"name": "@orvynmmo/cli", "version": "1.0.0", "description": "OrvynMMO Command Line Interface", "type": "module", "bin": {"orvyn": "./dist/index.js"}, "scripts": {"build": "esbuild src/index.ts --bundle --platform=node --target=node18 --outfile=dist/index.js --format=esm --banner:js=\"#!/usr/bin/env node\"", "dev": "tsx src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts --fix"}, "dependencies": {"commander": "^11.1.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "fs-extra": "^11.1.1", "glob": "^10.3.10", "semver": "^7.5.4", "yaml": "^2.3.4", "json5": "^2.2.3"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/semver": "^7.5.6", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "esbuild": "^0.19.8", "eslint": "^8.53.0", "tsx": "^4.1.4", "typescript": "^5.2.2", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0"}}