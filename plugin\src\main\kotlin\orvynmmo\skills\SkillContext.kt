package orvynmmo.skills

import orvynmmo.core.id.SkillId
import org.bukkit.Location
import org.bukkit.entity.Entity
import org.bukkit.entity.LivingEntity
import java.util.*
import kotlin.random.Random

/**
 * Context for skill execution containing all necessary data.
 * 
 * Provides access to caster, target, location, and execution state
 * in a thread-safe and deterministic manner.
 */
data class SkillContext(
    val caster: SkillCaster,
    val target: SkillTarget? = null,
    val targetLoc: Location? = null,
    val executionId: UUID = UUID.randomUUID(),
    val seed: Long = Random.nextLong(),
    val variables: MutableMap<String, Any> = mutableMapOf(),
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * Random number generator seeded for deterministic execution
     */
    val random: Random = Random(seed)
    
    /**
     * Execution start time in milliseconds
     */
    val startTime: Long = System.currentTimeMillis()
    
    /**
     * Gets a variable value with type safety
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> getVariable(key: String): T? {
        return variables[key] as? T
    }
    
    /**
     * Sets a variable value
     */
    fun setVariable(key: String, value: Any) {
        variables[key] = value
    }
    
    /**
     * Gets a variable with a default value
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> getVariable(key: String, default: T): T {
        return variables[key] as? T ?: default
    }
    
    /**
     * Checks if the caster is on cooldown for a skill
     */
    fun isOnCooldown(skillId: SkillId): Boolean {
        return caster.isOnCooldown(skillId)
    }
    
    /**
     * Gets the effective target location
     */
    fun getTargetLocation(): Location {
        return targetLoc ?: target?.location ?: caster.location
    }
    
    /**
     * Creates a child context for nested skill execution
     */
    fun createChildContext(
        newTarget: SkillTarget? = null,
        newLocation: Location? = null,
        additionalVariables: Map<String, Any> = emptyMap()
    ): SkillContext {
        return copy(
            target = newTarget ?: target,
            targetLoc = newLocation ?: targetLoc,
            executionId = UUID.randomUUID(),
            variables = (variables + additionalVariables).toMutableMap()
        )
    }
}

/**
 * Represents an entity that can cast skills
 */
interface SkillCaster {
    val entity: LivingEntity
    val location: Location
    val mana: Int
    val maxMana: Int
    
    /**
     * Gets an attribute value
     */
    fun getAttribute(name: String): Double?
    
    /**
     * Checks if on cooldown for a skill
     */
    fun isOnCooldown(skillId: SkillId): Boolean
    
    /**
     * Sets a cooldown for a skill
     */
    fun setCooldown(skillId: SkillId, duration: Long)
    
    /**
     * Consumes mana
     */
    fun consumeMana(amount: Int): Boolean
    
    /**
     * Restores mana
     */
    fun restoreMana(amount: Int)
}

/**
 * Represents a target for skills
 */
interface SkillTarget {
    val entity: Entity?
    val location: Location
    
    /**
     * Checks if this target is valid
     */
    fun isValid(): Boolean
    
    /**
     * Gets the distance to another location
     */
    fun distanceTo(other: Location): Double {
        return location.distance(other)
    }
}

/**
 * Entity-based skill target
 */
class EntityTarget(override val entity: Entity) : SkillTarget {
    override val location: Location
        get() = entity.location
    
    override fun isValid(): Boolean {
        return entity.isValid && !entity.isDead
    }
}

/**
 * Location-based skill target
 */
class LocationTarget(override val location: Location) : SkillTarget {
    override val entity: Entity? = null
    
    override fun isValid(): Boolean = true
}

/**
 * Default implementation of SkillCaster for players and mobs
 */
class DefaultSkillCaster(
    override val entity: LivingEntity,
    private val attributes: Map<String, Double> = emptyMap(),
    private val cooldowns: MutableMap<SkillId, Long> = mutableMapOf()
) : SkillCaster {
    
    override val location: Location
        get() = entity.location
    
    override val mana: Int
        get() = getAttribute("mana")?.toInt() ?: 100
    
    override val maxMana: Int
        get() = getAttribute("max_mana")?.toInt() ?: 100
    
    override fun getAttribute(name: String): Double? {
        return attributes[name]
    }
    
    override fun isOnCooldown(skillId: SkillId): Boolean {
        val cooldownEnd = cooldowns[skillId] ?: return false
        return System.currentTimeMillis() < cooldownEnd
    }
    
    override fun setCooldown(skillId: SkillId, duration: Long) {
        cooldowns[skillId] = System.currentTimeMillis() + duration
    }
    
    override fun consumeMana(amount: Int): Boolean {
        val currentMana = mana
        if (currentMana >= amount) {
            // In a real implementation, this would update the entity's mana
            return true
        }
        return false
    }
    
    override fun restoreMana(amount: Int) {
        // In a real implementation, this would update the entity's mana
    }
}
