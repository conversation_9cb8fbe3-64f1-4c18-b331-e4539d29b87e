#!/bin/bash

# OrvynMMO Development Server Startup Script
# This script starts a Paper/Folia server with the OrvynMMO plugin for development

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGIN_DIR="$(dirname "$SCRIPT_DIR")"
ROOT_DIR="$(dirname "$PLUGIN_DIR")"

echo "Starting OrvynMMO development server..."

# Check if paper.jar exists
if [ ! -f "$SCRIPT_DIR/paper.jar" ]; then
    echo "Error: paper.jar not found in $SCRIPT_DIR"
    echo "Please download Paper/Folia and place it as 'paper.jar' in the run directory"
    exit 1
fi

# Create plugins directory if it doesn't exist
mkdir -p "$SCRIPT_DIR/plugins"

# Find the shaded jar
SHADED_JAR=$(find "$PLUGIN_DIR/target" -name "OrvynMMO-*-shaded.jar" 2>/dev/null | head -n 1)

if [ -z "$SHADED_JAR" ]; then
    echo "Error: Shaded jar not found. Please run 'mvn package' first"
    exit 1
fi

echo "Found shaded jar: $SHADED_JAR"

# Copy the shaded jar to plugins directory
cp "$SHADED_JAR" "$SCRIPT_DIR/plugins/OrvynMMO.jar"
echo "Copied plugin to plugins/OrvynMMO.jar"

# Create server.properties if it doesn't exist
if [ ! -f "$SCRIPT_DIR/server.properties" ]; then
    cat > "$SCRIPT_DIR/server.properties" << EOF
# OrvynMMO Development Server Configuration
server-port=25565
gamemode=creative
difficulty=peaceful
spawn-protection=0
max-players=10
online-mode=false
enable-command-block=true
motd=OrvynMMO Development Server
EOF
    echo "Created default server.properties"
fi

# Create eula.txt if it doesn't exist
if [ ! -f "$SCRIPT_DIR/eula.txt" ]; then
    echo "eula=true" > "$SCRIPT_DIR/eula.txt"
    echo "Created eula.txt"
fi

# Change to run directory
cd "$SCRIPT_DIR"

# Start the server
echo "Starting server..."
java -Dpaper.disableChannelWarning=true \
     -Xms2G \
     -Xmx4G \
     -XX:+UseG1GC \
     -XX:+ParallelRefProcEnabled \
     -XX:MaxGCPauseMillis=200 \
     -XX:+UnlockExperimentalVMOptions \
     -XX:+DisableExplicitGC \
     -XX:+AlwaysPreTouch \
     -XX:G1NewSizePercent=30 \
     -XX:G1MaxNewSizePercent=40 \
     -XX:G1HeapRegionSize=8M \
     -XX:G1ReservePercent=20 \
     -XX:G1HeapWastePercent=5 \
     -XX:G1MixedGCCountTarget=4 \
     -XX:InitiatingHeapOccupancyPercent=15 \
     -XX:G1MixedGCLiveThresholdPercent=90 \
     -XX:G1RSetUpdatingPauseTimePercent=5 \
     -XX:SurvivorRatio=32 \
     -XX:+PerfDisableSharedMem \
     -XX:MaxTenuringThreshold=1 \
     -Dusing.aikars.flags=https://mcflags.emc.gs \
     -Daikars.new.flags=true \
     -jar paper.jar nogui
