package orvynmmo.core.id

/**
 * Type-safe identifier for OrvynMMO resources.
 * 
 * Uses value classes for zero-cost abstraction while maintaining type safety.
 * All IDs follow the format "namespace:key" where namespace defaults to "orvyn".
 */
@JvmInline
value class OrvynId(val value: String) {
    
    init {
        require(isValid(value)) { "Invalid OrvynId format: '$value'. Must match pattern 'namespace:key'" }
    }
    
    /**
     * The namespace part of the ID (before the colon)
     */
    val namespace: String
        get() = value.substringBefore(':')
    
    /**
     * The key part of the ID (after the colon)
     */
    val key: String
        get() = value.substringAfter(':')
    
    /**
     * Returns true if this ID is in the default "orvyn" namespace
     */
    val isOrvynNamespace: Boolean
        get() = namespace == DEFAULT_NAMESPACE
    
    override fun toString(): String = value
    
    companion object {
        const val DEFAULT_NAMESPACE = "orvyn"
        private val ID_PATTERN = Regex("^[a-z0-9_.-]+:[a-z0-9_./\\-]+$")
        
        /**
         * Creates an OrvynId from namespace and key
         */
        fun of(namespace: String, key: String): OrvynId {
            return OrvynId("$namespace:$key")
        }
        
        /**
         * Creates an OrvynId with the default namespace
         */
        fun orvyn(key: String): OrvynId {
            return OrvynId("$DEFAULT_NAMESPACE:$key")
        }
        
        /**
         * Parses a string into an OrvynId, adding default namespace if needed
         */
        fun parse(input: String): OrvynId {
            return if (input.contains(':')) {
                OrvynId(input)
            } else {
                orvyn(input)
            }
        }
        
        /**
         * Validates an ID string format
         */
        fun isValid(value: String): Boolean {
            return ID_PATTERN.matches(value)
        }
    }
}

/**
 * Typed ID for skills
 */
@JvmInline
value class SkillId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = SkillId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = SkillId(OrvynId.orvyn(key))
        fun parse(input: String) = SkillId(OrvynId.parse(input))
        fun fromString(value: String) = SkillId(OrvynId(value))
    }
}

/**
 * Typed ID for items
 */
@JvmInline
value class ItemId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = ItemId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = ItemId(OrvynId.orvyn(key))
        fun parse(input: String) = ItemId(OrvynId.parse(input))
        fun fromString(value: String) = ItemId(OrvynId(value))
    }
}

/**
 * Typed ID for RPG classes
 */
@JvmInline
value class ClassId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = ClassId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = ClassId(OrvynId.orvyn(key))
        fun parse(input: String) = ClassId(OrvynId.parse(input))
        fun fromString(value: String) = ClassId(OrvynId(value))
    }
}

/**
 * Typed ID for models
 */
@JvmInline
value class ModelId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = ModelId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = ModelId(OrvynId.orvyn(key))
        fun parse(input: String) = ModelId(OrvynId.parse(input))
        fun fromString(value: String) = ModelId(OrvynId(value))
    }
}

/**
 * Typed ID for drop tables
 */
@JvmInline
value class DropTableId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = DropTableId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = DropTableId(OrvynId.orvyn(key))
        fun parse(input: String) = DropTableId(OrvynId.parse(input))
        fun fromString(value: String) = DropTableId(OrvynId(value))
    }
}

/**
 * Typed ID for spawn configurations
 */
@JvmInline
value class SpawnId(val id: OrvynId) {
    val namespace: String get() = id.namespace
    val key: String get() = id.key

    override fun toString(): String = id.toString()

    companion object {
        fun of(namespace: String, key: String) = SpawnId(OrvynId.of(namespace, key))
        fun orvyn(key: String) = SpawnId(OrvynId.orvyn(key))
        fun parse(input: String) = SpawnId(OrvynId.parse(input))
        fun fromString(value: String) = SpawnId(OrvynId(value))
    }
}
