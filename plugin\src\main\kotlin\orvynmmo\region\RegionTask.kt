package orvynmmo.region

import org.bukkit.scheduler.BukkitTask

/**
 * Abstraction for scheduled tasks that works with both Folia and Paper/Spigot
 */
interface RegionTask {
    /**
     * Cancels the task
     */
    fun cancel()
    
    /**
     * Checks if the task is cancelled
     */
    fun isCancelled(): <PERSON><PERSON><PERSON>
}

/**
 * Bukkit/Paper implementation of RegionTask
 */
class BukkitRegionTask(private val bukkitTask: BukkitTask) : RegionTask {
    override fun cancel() {
        bukkitTask.cancel()
    }
    
    override fun isCancelled(): Boolean {
        return bukkitTask.isCancelled
    }
}

/**
 * Folia implementation of RegionTask using reflection
 */
class FoliaRegionTask(private val foliaTask: Any) : RegionTask {
    override fun cancel() {
        try {
            val cancelMethod = foliaTask.javaClass.getMethod("cancel")
            cancelMethod.invoke(foliaTask)
        } catch (e: Exception) {
            // Ignore if cancel fails
        }
    }
    
    override fun isCancelled(): <PERSON><PERSON>an {
        return try {
            val isCancelledMethod = foliaTask.javaClass.getMethod("isCancelled")
            isCancelledMethod.invoke(foliaTask) as Boolean
        } catch (e: Exception) {
            true // Assume cancelled if we can't check
        }
    }
}
