package orvynmmo.commands

import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.command.Command
import org.bukkit.command.CommandExecutor
import org.bukkit.command.CommandSender
import org.bukkit.command.TabCompleter
import orvynmmo.OrvynMMOPlugin

/**
 * Main command handler for OrvynMMO.
 */
class OrvynCommand(private val plugin: OrvynMMOPlugin) : CommandExecutor, TabCompleter {
    
    override fun onCommand(
        sender: CommandSender,
        command: Command,
        label: String,
        args: Array<out String>
    ): Boolean {
        
        if (args.isEmpty()) {
            sendHelp(sender)
            return true
        }
        
        when (args[0].lowercase()) {
            "reload" -> handleReload(sender, args)
            "profile" -> handleProfile(sender, args)
            "test" -> handleTest(sender, args)
            "import" -> handleImport(sender, args)
            "stats" -> handleStats(sender, args)
            else -> sendHelp(sender)
        }
        
        return true
    }
    
    override fun onTabComplete(
        sender: CommandS<PERSON>,
        command: Command,
        alias: String,
        args: Array<out String>
    ): List<String> {
        
        if (args.size == 1) {
            return listOf("reload", "profile", "test", "import", "stats")
                .filter { it.startsWith(args[0].lowercase()) }
        }
        
        return emptyList()
    }
    
    private fun sendHelp(sender: CommandSender) {
        sender.sendMessage(plugin.createPrefixedComponent("Available commands:", NamedTextColor.GOLD))
        sender.sendMessage(plugin.createPrefixedComponent("  /orvyn reload - Reload configuration", NamedTextColor.YELLOW))
        sender.sendMessage(plugin.createPrefixedComponent("  /orvyn profile - Show performance profile", NamedTextColor.YELLOW))
        sender.sendMessage(plugin.createPrefixedComponent("  /orvyn test <encounter> - Run test encounter", NamedTextColor.YELLOW))
        sender.sendMessage(plugin.createPrefixedComponent("  /orvyn import <type> - Import from other plugins", NamedTextColor.YELLOW))
        sender.sendMessage(plugin.createPrefixedComponent("  /orvyn stats - Show system statistics", NamedTextColor.YELLOW))
    }
    
    private fun handleReload(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("orvyn.admin.reload")) {
            sender.sendMessage(plugin.createPrefixedComponent("No permission", NamedTextColor.RED))
            return
        }
        
        val dryRun = args.contains("--dry-run")
        
        if (dryRun) {
            sender.sendMessage(plugin.createPrefixedComponent("Dry run reload completed", NamedTextColor.GREEN))
        } else {
            val success = plugin.hotReloadManager.reload()
            if (success) {
                sender.sendMessage(plugin.createPrefixedComponent("Reload completed successfully", NamedTextColor.GREEN))
            } else {
                sender.sendMessage(plugin.createPrefixedComponent("Reload failed", NamedTextColor.RED))
            }
        }
    }
    
    private fun handleProfile(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("orvyn.admin.profile")) {
            sender.sendMessage(plugin.createPrefixedComponent("No permission", NamedTextColor.RED))
            return
        }
        
        val stats = plugin.profiler.getStats()
        sender.sendMessage(plugin.createPrefixedComponent("Profiler: $stats", NamedTextColor.AQUA))
    }
    
    private fun handleTest(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("orvyn.admin.test")) {
            sender.sendMessage(plugin.createPrefixedComponent("No permission", NamedTextColor.RED))
            return
        }
        
        if (args.size < 2) {
            sender.sendMessage(plugin.createPrefixedComponent("Usage: /orvyn test <encounter>", NamedTextColor.RED))
            return
        }
        
        val encounter = args[1]
        sender.sendMessage(plugin.createPrefixedComponent("Running test encounter: $encounter", NamedTextColor.GREEN))
    }
    
    private fun handleImport(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("orvyn.admin.import")) {
            sender.sendMessage(plugin.createPrefixedComponent("No permission", NamedTextColor.RED))
            return
        }
        
        if (args.size < 2) {
            sender.sendMessage(plugin.createPrefixedComponent("Usage: /orvyn import <type>", NamedTextColor.RED))
            return
        }
        
        val type = args[1]
        sender.sendMessage(plugin.createPrefixedComponent("Import from $type not yet implemented", NamedTextColor.YELLOW))
    }
    
    private fun handleStats(sender: CommandSender, args: Array<out String>) {
        if (!sender.hasPermission("orvyn.profile")) {
            sender.sendMessage(plugin.createPrefixedComponent("No permission", NamedTextColor.RED))
            return
        }
        
        val coreStats = plugin.core.getStats()
        sender.sendMessage(plugin.createPrefixedComponent("System Statistics:", NamedTextColor.GOLD))
        sender.sendMessage(plugin.createPrefixedComponent("  Core: $coreStats", NamedTextColor.AQUA))
    }
}
