{"name": "@orvynmmo/editor", "version": "1.0.0", "description": "OrvynMMO Visual Editor", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "zustand": "^4.4.7", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "tailwindcss": "^3.3.6", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.158.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "typescript": "^5.2.2", "vite": "^4.5.0"}, "engines": {"node": ">=18.0.0"}}