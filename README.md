# OrvynMMO

> Next-gen, Folia-safe, cinematic MMO framework for Paper/Purpur

[![CI](https://github.com/orvynmmo/orvynmmo/actions/workflows/ci.yml/badge.svg)](https://github.com/orvynmmo/orvynmmo/actions/workflows/ci.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

OrvynMMO is a comprehensive MMO framework that replaces MythicMobs/MMOItems/MMOCore with a unified, typed, ergonomic platform designed for modern Minecraft servers.

## 🚀 Key Features

- **Folia-First**: Region-thread safety enforced by design with zero main-thread I/O
- **Visual Authoring**: Node editor + wizards + live preview + strict validation
- **Hot-Reload**: Crash-proof hot-reload with transactional updates
- **Compatibility**: Import from MythicMobs/MMOItems/MMOCore with conversion reports
- **Creator Economy**: Signed pack format with marketplace hooks
- **Observability**: Built-in profiler, budgeter, and performance monitoring

## 📦 Architecture

```
orvynmmo/
├── plugin/          # Kotlin server plugin (Maven)
├── editor/          # React + TypeScript visual editor
├── cli/             # Node.js command-line tools
├── schemas/         # JSON Schema definitions
├── examples/        # Demo content packs
└── docs/           # Documentation
```

## 🛠️ Quick Start

### Prerequisites

- **Java 17+** (for plugin development)
- **Node.js 18+** (for editor and CLI)
- **Maven 3.8+** (for building the plugin)
- **Paper/Purpur 1.21+** (recommended: Folia for best performance)

### Building

```bash
# Build the plugin
mvn -q -B clean package

# Build the editor
cd editor && npm install && npm run build

# Build the CLI
cd cli && npm install && npm run build
```

### Development Server

1. **Download Paper/Folia** and place it as `plugin/run/paper.jar`
2. **Build the plugin**: `mvn -q -B package`
3. **Start the server**: `cd plugin/run && ./start-dev.sh` (or `.ps1` on Windows)
4. **Start the editor**: `cd editor && npm run dev`

The editor will be available at `http://localhost:3000` and will connect to the development server.

## 📚 Documentation

- [Getting Started](docs/getting-started.md) - 10-minute boss tutorial
- [Authoring Guide](docs/authoring/) - Skills, items, RPG, models, drops, spawns
- [Migration Guide](docs/migration/) - From MythicMobs/MMOItems/MMOCore
- [Performance Guide](docs/performance.md) - Folia rules, budgets, profiling
- [API Reference](docs/api/) - Plugin and editor APIs

## 🎮 Demo Content

The repository includes demo content to get you started:

- **Storm Seraph Boss** - 3-phase encounter with chain lightning, ground AoE, and summons
- **Arcanist Class** - 10-node skill tree with lightning-themed abilities
- **Legendary Item Set** - Arc Javelin, Seraphic Aegis, Plasmic Treads

Try it out:
```bash
# Import the demo pack
orvyn pack install examples/packs/demo-boss

# Test the encounter
orvyn test run demo-boss --scenario storm-seraph
```

## 🔧 CLI Usage

```bash
# Pack management
orvyn pack init my-pack
orvyn pack validate
orvyn pack compile
orvyn pack sign --key private.pem

# Import from other plugins
orvyn import mythic ./MythicMobs/
orvyn import mmoitems ./MMOItems/

# Testing and validation
orvyn test run my-pack --scenario boss-fight
orvyn doctor  # Environment check
```

## 🎨 Visual Editor

The web-based editor provides:

- **Skill Graph Designer** - Visual node-based skill creation
- **Item Designer** - Stats, tiers, sets, affixes, abilities
- **RPG Designer** - Classes, attributes, skill trees
- **3D Preview** - Real-time particle and animation preview
- **Pack Manager** - Version control, dependencies, publishing

## 🔄 Migration from Existing Plugins

OrvynMMO provides seamless migration tools:

```bash
# Import MythicMobs skills
orvyn import mythic ./plugins/MythicMobs/ --output ./my-pack/

# Import MMOItems
orvyn import mmoitems ./plugins/MMOItems/ --output ./my-pack/

# Import MMOCore classes
orvyn import mmocore ./plugins/MMOCore/ --output ./my-pack/
```

Each import generates a detailed conversion report showing:
- ✅ Successfully converted features
- ⚠️ Partially converted features with manual steps
- ❌ Unsupported features with alternatives

## 📊 Performance & Monitoring

Built-in performance monitoring with configurable budgets:

- **Time Budget**: ≤35% per region frame for mechanics
- **Particle Budget**: ≤5k particles per tick/region
- **Sound Budget**: ≤64 sounds per tick/region
- **Entity Budget**: ≤32 summons per tick/region

Monitor with `/orvyn profile` or the web dashboard.

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass: `mvn test && npm test`
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Paper Team** - For the excellent server software
- **MythicMobs/MMOItems/MMOCore** - For inspiration and compatibility
- **Folia Team** - For pushing Minecraft server performance forward
- **Community** - For feedback and contributions

---

**Ready to build the next generation of Minecraft MMO content?** 🚀

[Get Started](docs/getting-started.md) | [Join Discord](https://discord.gg/orvynmmo) | [View Examples](examples/)
