package orvynmmo

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.format.NamedTextColor
import org.bukkit.plugin.java.JavaPlugin
import orvynmmo.commands.OrvynCommand
import orvynmmo.core.OrvynMMOCore
import orvynmmo.core.registry.RegistryManager
import orvynmmo.obs.Profiler
import orvynmmo.region.RegionManager
import orvynmmo.runtime.HotReloadManager
import java.util.logging.Level

/**
 * Main OrvynMMO plugin class.
 * 
 * This plugin is designed to be Folia-safe and provides a next-generation
 * MMO framework for Paper/Purpur servers.
 */
class OrvynMMOPlugin : JavaPlugin() {
    
    companion object {
        lateinit var instance: OrvynMMOPlugin
            private set
        
        const val PLUGIN_PREFIX = "[OrvynMMO]"
    }
    
    // Core systems
    lateinit var core: OrvynMMOCore
        private set
    
    lateinit var registryManager: RegistryManager
        private set
    
    lateinit var regionManager: RegionManager
        private set
    
    lateinit var hotReloadManager: HotReloadManager
        private set
    
    lateinit var profiler: Profiler
        private set
    
    // Coroutine scope for the plugin
    private val pluginScope = CoroutineScope(SupervisorJob())
    
    override fun onLoad() {
        instance = this
        
        logger.info("$PLUGIN_PREFIX Loading OrvynMMO v${description.version}")
        
        // Check if running on Folia
        val isFolia = try {
            Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
            true
        } catch (e: ClassNotFoundException) {
            false
        }
        
        if (isFolia) {
            logger.info("$PLUGIN_PREFIX Detected Folia - enabling region-aware mode")
        } else {
            logger.info("$PLUGIN_PREFIX Running on Paper/Spigot - using compatibility mode")
        }
    }
    
    override fun onEnable() {
        try {
            logger.info("$PLUGIN_PREFIX Enabling OrvynMMO...")
            
            // Initialize core systems in order
            initializeCoreComponents()
            
            // Register commands
            registerCommands()
            
            // Start profiler
            profiler.start()
            
            logger.info("$PLUGIN_PREFIX Successfully enabled!")
            
        } catch (e: Exception) {
            logger.log(Level.SEVERE, "$PLUGIN_PREFIX Failed to enable plugin", e)
            server.pluginManager.disablePlugin(this)
        }
    }
    
    override fun onDisable() {
        logger.info("$PLUGIN_PREFIX Disabling OrvynMMO...")
        
        try {
            // Stop profiler
            if (::profiler.isInitialized) {
                profiler.stop()
            }
            
            // Cancel all coroutines
            pluginScope.cancel()
            
            // Shutdown core systems
            if (::core.isInitialized) {
                core.shutdown()
            }
            
            logger.info("$PLUGIN_PREFIX Successfully disabled!")
            
        } catch (e: Exception) {
            logger.log(Level.SEVERE, "$PLUGIN_PREFIX Error during shutdown", e)
        }
    }
    
    private fun initializeCoreComponents() {
        // Initialize registry manager first
        registryManager = RegistryManager()
        
        // Initialize region manager for Folia support
        regionManager = RegionManager(this)
        
        // Initialize profiler
        profiler = Profiler(this)
        
        // Initialize hot reload manager
        hotReloadManager = HotReloadManager(this, registryManager)
        
        // Initialize core system
        core = OrvynMMOCore(
            plugin = this,
            registryManager = registryManager,
            regionManager = regionManager,
            profiler = profiler,
            scope = pluginScope
        )
        
        // Initialize core
        core.initialize()
    }
    
    private fun registerCommands() {
        val orvynCommand = OrvynCommand(this)
        getCommand("orvyn")?.setExecutor(orvynCommand)
        getCommand("orvyn")?.tabCompleter = orvynCommand
    }
    
    /**
     * Sends a message to console with the plugin prefix
     */
    fun logInfo(message: String) {
        logger.info("$PLUGIN_PREFIX $message")
    }
    
    /**
     * Sends a warning to console with the plugin prefix
     */
    fun logWarning(message: String) {
        logger.warning("$PLUGIN_PREFIX $message")
    }
    
    /**
     * Sends an error to console with the plugin prefix
     */
    fun logError(message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            logger.log(Level.SEVERE, "$PLUGIN_PREFIX $message", throwable)
        } else {
            logger.severe("$PLUGIN_PREFIX $message")
        }
    }
    
    /**
     * Creates a formatted component with the OrvynMMO prefix
     */
    fun createPrefixedComponent(message: String, color: NamedTextColor = NamedTextColor.WHITE): Component {
        return Component.text()
            .append(Component.text("[", NamedTextColor.DARK_GRAY))
            .append(Component.text("OrvynMMO", NamedTextColor.GOLD))
            .append(Component.text("] ", NamedTextColor.DARK_GRAY))
            .append(Component.text(message, color))
            .build()
    }
}
