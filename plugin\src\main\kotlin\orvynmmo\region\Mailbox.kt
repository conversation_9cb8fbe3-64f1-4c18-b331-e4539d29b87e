package orvynmmo.region

import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ReceiveChannel
import kotlinx.coroutines.channels.SendChannel
import org.bukkit.Location
import org.bukkit.entity.Entity
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Cross-region communication system for Folia.
 * 
 * Provides safe message passing between different regions without
 * blocking or causing thread safety issues.
 */
class Mailbox(private val regionManager: RegionManager) {
    
    private val messageId = AtomicLong(0)
    private val pendingMessages = ConcurrentHashMap<Long, CompletableFuture<Any?>>()
    
    /**
     * Sends a message to be processed on the target location's region
     */
    fun <T> sendMessage(
        targetLocation: Location,
        message: MailboxMessage<T>
    ): CompletableFuture<T> {
        val id = messageId.incrementAndGet()
        val future = CompletableFuture<T>()
        
        @Suppress("UNCHECKED_CAST")
        pendingMessages[id] = future as CompletableFuture<Any?>
        
        val regionGate = regionManager.getRegionGate(targetLocation)
        
        regionGate.execute {
            try {
                val result = message.process()
                future.complete(result)
            } catch (e: Exception) {
                future.completeExceptionally(e)
            } finally {
                pendingMessages.remove(id)
            }
        }
        
        return future
    }
    
    /**
     * Sends a message to be processed on the target entity's region
     */
    fun <T> sendMessage(
        targetEntity: Entity,
        message: MailboxMessage<T>
    ): CompletableFuture<T> {
        return sendMessage(targetEntity.location, message)
    }
    
    /**
     * Sends a message without expecting a response
     */
    fun sendFireAndForget(
        targetLocation: Location,
        action: () -> Unit
    ) {
        val regionGate = regionManager.getRegionGate(targetLocation)
        regionGate.execute(action)
    }
    
    /**
     * Sends a message to an entity without expecting a response
     */
    fun sendFireAndForget(
        targetEntity: Entity,
        action: () -> Unit
    ) {
        sendFireAndForget(targetEntity.location, action)
    }
    
    /**
     * Creates a message channel for streaming communication
     */
    fun <T> createChannel(
        targetLocation: Location,
        capacity: Int = Channel.UNLIMITED
    ): MailboxChannel<T> {
        return MailboxChannel(regionManager, targetLocation, capacity)
    }
    
    /**
     * Broadcasts a message to multiple locations
     */
    fun <T> broadcast(
        locations: Collection<Location>,
        message: MailboxMessage<T>
    ): List<CompletableFuture<T>> {
        return locations.map { location ->
            sendMessage(location, message)
        }
    }
    
    /**
     * Waits for all pending messages to complete
     */
    suspend fun awaitAll(timeout: Long = 5000): Boolean {
        return withTimeoutOrNull(timeout) {
            while (pendingMessages.isNotEmpty()) {
                delay(10)
            }
            true
        } ?: false
    }
    
    /**
     * Gets statistics about mailbox usage
     */
    fun getStats(): MailboxStats {
        return MailboxStats(
            pendingMessages = pendingMessages.size,
            totalMessagesSent = messageId.get()
        )
    }
    
    /**
     * Cleans up expired messages
     */
    fun cleanup() {
        val iterator = pendingMessages.entries.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.isDone || entry.value.isCancelled) {
                iterator.remove()
            }
        }
    }
}

/**
 * A message that can be processed on a target region
 */
fun interface MailboxMessage<T> {
    /**
     * Processes the message and returns a result
     */
    fun process(): T
}

/**
 * Channel for streaming communication between regions
 */
class MailboxChannel<T>(
    private val regionManager: RegionManager,
    private val targetLocation: Location,
    capacity: Int
) {
    private val channel = Channel<T>(capacity)
    private val regionGate = regionManager.getRegionGate(targetLocation)
    
    /**
     * Sends a value to the channel
     */
    suspend fun send(value: T) {
        channel.send(value)
    }
    
    /**
     * Receives a value from the channel on the target region
     */
    suspend fun receive(): T {
        return regionGate.executeSuspending {
            channel.receive()
        }
    }
    
    /**
     * Closes the channel
     */
    fun close() {
        channel.close()
    }
    
    /**
     * Gets the send channel
     */
    val sendChannel: SendChannel<T> = channel
    
    /**
     * Gets the receive channel
     */
    val receiveChannel: ReceiveChannel<T> = channel
}

/**
 * Statistics about mailbox usage
 */
data class MailboxStats(
    val pendingMessages: Int,
    val totalMessagesSent: Long
) {
    override fun toString(): String {
        return "MailboxStats(pending=$pendingMessages, total=$totalMessagesSent)"
    }
}
