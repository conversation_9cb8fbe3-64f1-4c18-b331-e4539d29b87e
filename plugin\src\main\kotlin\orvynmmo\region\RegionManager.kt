package orvynmmo.region

import kotlinx.coroutines.*
import org.bukkit.Bukkit
import org.bukkit.Location
import org.bukkit.entity.Entity
import org.bukkit.plugin.Plugin
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap
import java.util.function.Consumer

/**
 * Manages region-aware operations for Folia compatibility.
 * 
 * Provides a unified API that works on both Folia and Paper/Spigot,
 * automatically routing operations to the correct thread/region.
 */
class RegionManager(private val plugin: Plugin) {
    
    private val isFolia: Boolean = try {
        Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
        true
    } catch (e: ClassNotFoundException) {
        false
    }
    
    private val regionGates = ConcurrentHashMap<String, RegionGate>()
    
    /**
     * Gets or creates a region gate for the given location
     */
    fun getRegionGate(location: Location): RegionGate {
        val regionKey = if (isFolia) {
            // On Folia, use chunk coordinates as region key
            "${location.world?.name}:${location.blockX shr 4}:${location.blockZ shr 4}"
        } else {
            // On Paper/Spigot, use world name as region key
            location.world?.name ?: "unknown"
        }
        
        return regionGates.computeIfAbsent(regionKey) { 
            RegionGate(plugin, location, isFolia)
        }
    }
    
    /**
     * Gets or creates a region gate for the given entity
     */
    fun getRegionGate(entity: Entity): RegionGate {
        return getRegionGate(entity.location)
    }
    
    /**
     * Executes a task on the appropriate region thread
     */
    fun runOnRegion(location: Location, task: Runnable) {
        getRegionGate(location).execute(task)
    }
    
    /**
     * Executes a task on the appropriate region thread with a result
     */
    fun <T> runOnRegion(location: Location, task: () -> T): CompletableFuture<T> {
        return getRegionGate(location).execute(task)
    }
    
    /**
     * Executes a task on the entity's region thread
     */
    fun runOnEntityRegion(entity: Entity, task: Runnable) {
        getRegionGate(entity).execute(task)
    }
    
    /**
     * Executes a task on the entity's region thread with a result
     */
    fun <T> runOnEntityRegion(entity: Entity, task: () -> T): CompletableFuture<T> {
        return getRegionGate(entity).execute(task)
    }
    
    /**
     * Creates a mailbox for cross-region communication
     */
    fun createMailbox(): Mailbox {
        return Mailbox(this)
    }
    
    /**
     * Checks if the current thread owns the region for the given location
     */
    fun isRegionThread(location: Location): Boolean {
        return if (isFolia) {
            // On Folia, check if we're on the region thread
            try {
                val regionizedServer = Class.forName("io.papermc.paper.threadedregions.RegionizedServer")
                val isOwnedByCurrentRegion = regionizedServer.getMethod("isOwnedByCurrentRegion", Location::class.java)
                isOwnedByCurrentRegion.invoke(null, location) as Boolean
            } catch (e: Exception) {
                false
            }
        } else {
            // On Paper/Spigot, check if we're on the main thread
            Bukkit.isPrimaryThread()
        }
    }
    
    /**
     * Gets statistics about region usage
     */
    fun getStats(): RegionStats {
        return RegionStats(
            isFolia = isFolia,
            regionGateCount = regionGates.size,
            activeRegions = regionGates.values.count { it.isActive }
        )
    }
    
    /**
     * Cleans up inactive region gates
     */
    fun cleanup() {
        val iterator = regionGates.entries.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (!entry.value.isActive) {
                entry.value.shutdown()
                iterator.remove()
            }
        }
    }
}

/**
 * Statistics about region manager usage
 */
data class RegionStats(
    val isFolia: Boolean,
    val regionGateCount: Int,
    val activeRegions: Int
) {
    override fun toString(): String {
        return "RegionStats(folia=$isFolia, gates=$regionGateCount, active=$activeRegions)"
    }
}
