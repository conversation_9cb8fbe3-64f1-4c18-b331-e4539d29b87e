# Getting Started with <PERSON><PERSON><PERSON><PERSON>

This guide will walk you through creating your first boss encounter in OrvynMMO in under 10 minutes.

## Prerequisites

- Java 17+ installed
- Paper/Folia server (1.21+)
- Basic understanding of Minecraft mechanics

## Step 1: Installation

### Download OrvynMMO

1. Download the latest release from [GitHub Releases](https://github.com/orvynmmo/orvynmmo/releases)
2. Place `OrvynMMO.jar` in your server's `plugins/` folder
3. Start your server

### Verify Installation

```
/orvyn stats
```

You should see system statistics confirming Orvyn<PERSON><PERSON> is loaded.

## Step 2: Create Your First Skill

Let's create a simple fireball skill using the CLI:

```bash
# Initialize a new pack
orvyn pack init my-first-pack

# Navigate to the pack directory
cd my-first-pack
```

Create `skills/fireball.json`:

```json
{
  "id": "mypack:fireball",
  "displayName": "Fireball",
  "description": "Hurls a blazing fireball at the target",
  "icon": "minecraft:fire_charge",
  "cooldown": 2000,
  "manaCost": 10,
  "castTime": 500,
  "range": 20.0,
  "targetType": "single_entity",
  "mechanics": [
    {
      "type": "particle",
      "tick": 0,
      "parameters": {
        "particle": "FLAME",
        "count": 10,
        "spread": 0.3
      }
    },
    {
      "type": "sound",
      "tick": 5,
      "parameters": {
        "sound": "ENTITY_BLAZE_SHOOT",
        "volume": 1.0,
        "pitch": 1.0
      }
    },
    {
      "type": "damage",
      "tick": 10,
      "parameters": {
        "amount": 15.0,
        "damageType": "fire"
      }
    }
  ]
}
```

## Step 3: Validate and Compile

```bash
# Validate your pack
orvyn pack validate

# Compile to intermediate representation
orvyn pack compile
```

## Step 4: Load into Server

```bash
# Install the pack on your server
orvyn pack install . --server localhost:25565
```

Or manually copy the compiled pack to your server's `plugins/OrvynMMO/packs/` directory and reload:

```
/orvyn reload
```

## Step 5: Test Your Skill

In-game, use the test command:

```
/orvyn test skill mypack:fireball
```

## Step 6: Create a Simple Boss

Create `spawns/fire_elemental.json`:

```json
{
  "id": "mypack:fire_elemental",
  "displayName": "Fire Elemental",
  "entityType": "BLAZE",
  "health": 200.0,
  "skills": [
    {
      "skillId": "mypack:fireball",
      "cooldown": 3000,
      "conditions": [
        {
          "type": "target_in_range",
          "parameters": {
            "range": 20.0
          }
        }
      ]
    }
  ],
  "drops": [
    {
      "item": "minecraft:blaze_rod",
      "chance": 0.8,
      "amount": "1-3"
    }
  ]
}
```

## Step 7: Spawn Your Boss

```
/orvyn spawn mypack:fire_elemental
```

## Next Steps

Congratulations! You've created your first OrvynMMO skill and boss. Here's what to explore next:

### Visual Editor
- Open the web editor at `http://localhost:3000` (if running dev server)
- Use the visual skill graph designer
- Preview particles and effects in real-time

### Advanced Features
- **Multi-phase bosses** with conditional mechanics
- **Custom items** with stats and abilities  
- **RPG classes** with skill trees
- **Drop tables** with weighted chances
- **Model integration** with ModelEngine/BetterModel

### Import Existing Content
```bash
# Import from MythicMobs
orvyn import mythic ./plugins/MythicMobs/

# Import from MMOItems  
orvyn import mmoitems ./plugins/MMOItems/
```

### Performance Monitoring
```
/orvyn profile --hud on
```

## Common Issues

### "Skill not found" Error
- Ensure your pack is properly compiled and loaded
- Check the skill ID format (namespace:key)
- Verify with `/orvyn stats` that skills are registered

### Performance Warnings
- Check `/orvyn profile` for budget usage
- Reduce particle counts or mechanic frequency
- Use conditions to limit skill execution

### Import Issues
- Ensure source plugin data is accessible
- Check conversion reports for unsupported features
- Manually convert complex mechanics if needed

## Getting Help

- **Documentation**: [docs/](../docs/)
- **Examples**: [examples/packs/](../examples/packs/)
- **Discord**: [Join our community](https://discord.gg/orvynmmo)
- **Issues**: [GitHub Issues](https://github.com/orvynmmo/orvynmmo/issues)

Ready to build something amazing? Check out the [Authoring Guide](authoring/) for advanced techniques!
