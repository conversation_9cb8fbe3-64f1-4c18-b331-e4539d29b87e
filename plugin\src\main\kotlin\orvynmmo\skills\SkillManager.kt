package orvynmmo.skills

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import orvynmmo.OrvynMMOPlugin
import orvynmmo.core.id.SkillId
import orvynmmo.core.registry.RegistryManager
import orvynmmo.obs.Profiler
import orvynmmo.region.RegionManager
import orvynmmo.skills.execution.SkillExecutor
import orvynmmo.skills.timeline.TimelineManager
import org.bukkit.entity.LivingEntity
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap

/**
 * Manages skill execution and lifecycle.
 * 
 * Coordinates skill casting, timeline execution, and cleanup in a
 * thread-safe and Folia-compatible manner.
 */
class SkillManager(
    private val plugin: OrvynMMOPlugin,
    private val registryManager: RegistryManager,
    private val regionManager: RegionManager,
    private val profiler: Profiler,
    private val scope: CoroutineScope
) {
    
    private lateinit var skillExecutor: SkillExecutor
    private lateinit var timelineManager: TimelineManager
    
    // Active skill executions
    private val activeExecutions = ConcurrentHashMap<String, SkillExecution>()
    
    /**
     * Initializes the skill manager
     */
    fun initialize() {
        plugin.logInfo("Initializing skill manager...")
        
        skillExecutor = SkillExecutor(plugin, regionManager, profiler)
        timelineManager = TimelineManager(plugin, regionManager, scope)
        
        plugin.logInfo("Skill manager initialized")
    }
    
    /**
     * Shuts down the skill manager
     */
    fun shutdown() {
        plugin.logInfo("Shutting down skill manager...")
        
        // Cancel all active executions
        activeExecutions.values.forEach { execution ->
            execution.cancel()
        }
        activeExecutions.clear()
        
        if (::timelineManager.isInitialized) {
            timelineManager.shutdown()
        }
        
        plugin.logInfo("Skill manager shut down")
    }
    
    /**
     * Casts a skill
     */
    fun castSkill(
        skillId: SkillId,
        caster: SkillCaster,
        target: SkillTarget? = null,
        targetLocation: org.bukkit.Location? = null
    ): CompletableFuture<SkillResult> {
        
        val skill = registryManager.getSkill(skillId)
            ?: return CompletableFuture.completedFuture(
                SkillResult.failure("Skill not found: $skillId")
            )
        
        val context = SkillContext(
            caster = caster,
            target = target,
            targetLoc = targetLocation
        )
        
        // Check if skill can be cast
        if (!skill.canCast(context)) {
            return CompletableFuture.completedFuture(
                SkillResult.failure("Cannot cast skill: $skillId")
            )
        }
        
        // Create execution
        val execution = SkillExecution(
            skill = skill,
            context = context,
            startTime = System.currentTimeMillis()
        )
        
        activeExecutions[execution.id] = execution
        
        // Execute skill asynchronously
        return scope.launch {
            try {
                val result = skillExecutor.execute(execution)
                activeExecutions.remove(execution.id)
                result
            } catch (e: Exception) {
                activeExecutions.remove(execution.id)
                SkillResult.failure("Skill execution failed: ${e.message}")
            }
        }.let { job ->
            val future = CompletableFuture<SkillResult>()
            job.invokeOnCompletion { throwable ->
                if (throwable != null) {
                    future.completeExceptionally(throwable)
                }
            }
            future
        }
    }
    
    /**
     * Cancels a skill execution
     */
    fun cancelExecution(executionId: String): Boolean {
        val execution = activeExecutions[executionId]
        return if (execution != null) {
            execution.cancel()
            activeExecutions.remove(executionId)
            true
        } else {
            false
        }
    }
    
    /**
     * Gets all active executions for an entity
     */
    fun getActiveExecutions(entity: LivingEntity): List<SkillExecution> {
        return activeExecutions.values.filter { 
            it.context.caster.entity == entity 
        }
    }
    
    /**
     * Cancels all executions for an entity
     */
    fun cancelAllExecutions(entity: LivingEntity) {
        getActiveExecutions(entity).forEach { execution ->
            execution.cancel()
            activeExecutions.remove(execution.id)
        }
    }
    
    /**
     * Gets skill manager statistics
     */
    fun getStats(): SkillManagerStats {
        return SkillManagerStats(
            activeExecutions = activeExecutions.size,
            totalSkills = registryManager.skills.size
        )
    }
}

/**
 * Represents an active skill execution
 */
data class SkillExecution(
    val skill: Skill,
    val context: SkillContext,
    val startTime: Long,
    val id: String = context.executionId.toString()
) {
    @Volatile
    private var cancelled = false
    
    /**
     * Cancels this execution
     */
    fun cancel() {
        cancelled = true
    }
    
    /**
     * Checks if this execution is cancelled
     */
    fun isCancelled(): Boolean = cancelled
    
    /**
     * Gets the execution duration in milliseconds
     */
    fun getDuration(): Long = System.currentTimeMillis() - startTime
}

/**
 * Result of a skill execution
 */
sealed class SkillResult {
    data class Success(val message: String = "Skill executed successfully") : SkillResult()
    data class Failure(val error: String) : SkillResult()
    
    companion object {
        fun success(message: String = "Skill executed successfully") = Success(message)
        fun failure(error: String) = Failure(error)
    }
}

/**
 * Statistics about skill manager
 */
data class SkillManagerStats(
    val activeExecutions: Int,
    val totalSkills: Int
) {
    override fun toString(): String {
        return "SkillManagerStats(active=$activeExecutions, total=$totalSkills)"
    }
}
