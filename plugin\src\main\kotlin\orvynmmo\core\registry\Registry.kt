package orvynmmo.core.registry

import orvynmmo.core.id.OrvynId
import java.util.concurrent.ConcurrentHashMap

/**
 * Immutable registry for storing typed resources.
 * 
 * Registries are immutable snapshots that can be atomically swapped during hot-reload.
 * All operations are thread-safe and designed for high-performance lookups.
 */
class Registry<T : Any> private constructor(
    private val entries: Map<OrvynId, T>
) {
    
    /**
     * Gets an entry by ID, or null if not found
     */
    operator fun get(id: OrvynId): T? = entries[id]
    
    /**
     * Gets an entry by ID, throwing if not found
     */
    fun getRequired(id: OrvynId): T = entries[id] 
        ?: throw IllegalArgumentException("No entry found for ID: $id")
    
    /**
     * Checks if an entry exists
     */
    fun contains(id: OrvynId): Boolean = id in entries
    
    /**
     * Gets all entries
     */
    fun getAll(): Map<OrvynId, T> = entries
    
    /**
     * Gets all IDs
     */
    fun getIds(): Set<OrvynId> = entries.keys
    
    /**
     * Gets all values
     */
    fun getValues(): Collection<T> = entries.values
    
    /**
     * Gets entries by namespace
     */
    fun getByNamespace(namespace: String): Map<OrvynId, T> {
        return entries.filterKeys { it.namespace == namespace }
    }
    
    /**
     * Gets the size of the registry
     */
    val size: Int get() = entries.size
    
    /**
     * Checks if the registry is empty
     */
    val isEmpty: Boolean get() = entries.isEmpty()
    
    /**
     * Creates a diff between this registry and another
     */
    fun diff(other: Registry<T>): RegistryDiff<T> {
        val added = mutableMapOf<OrvynId, T>()
        val removed = mutableMapOf<OrvynId, T>()
        val modified = mutableMapOf<OrvynId, Pair<T, T>>()
        
        // Find added and modified entries
        for ((id, newValue) in other.entries) {
            val oldValue = entries[id]
            if (oldValue == null) {
                added[id] = newValue
            } else if (oldValue != newValue) {
                modified[id] = oldValue to newValue
            }
        }
        
        // Find removed entries
        for ((id, oldValue) in entries) {
            if (id !in other.entries) {
                removed[id] = oldValue
            }
        }
        
        return RegistryDiff(added, removed, modified)
    }
    
    companion object {
        /**
         * Creates an empty registry
         */
        fun <T : Any> empty(): Registry<T> = Registry(emptyMap())
        
        /**
         * Creates a registry from a map
         */
        fun <T : Any> of(entries: Map<OrvynId, T>): Registry<T> = Registry(entries.toMap())
    }
}

/**
 * Builder for creating registries
 */
class RegistryBuilder<T : Any> {
    private val entries = mutableMapOf<OrvynId, T>()
    
    /**
     * Adds an entry to the registry
     */
    fun put(id: OrvynId, value: T): RegistryBuilder<T> {
        entries[id] = value
        return this
    }
    
    /**
     * Adds multiple entries to the registry
     */
    fun putAll(map: Map<OrvynId, T>): RegistryBuilder<T> {
        entries.putAll(map)
        return this
    }
    
    /**
     * Builds the immutable registry
     */
    fun build(): Registry<T> = Registry.of(entries)
}

/**
 * Represents the difference between two registries
 */
data class RegistryDiff<T : Any>(
    val added: Map<OrvynId, T>,
    val removed: Map<OrvynId, T>,
    val modified: Map<OrvynId, Pair<T, T>>
) {
    
    /**
     * Checks if there are any changes
     */
    val hasChanges: Boolean
        get() = added.isNotEmpty() || removed.isNotEmpty() || modified.isNotEmpty()
    
    /**
     * Gets the total number of changes
     */
    val changeCount: Int
        get() = added.size + removed.size + modified.size
    
    /**
     * Creates a summary of the changes
     */
    fun summary(): String {
        val parts = mutableListOf<String>()
        if (added.isNotEmpty()) parts.add("${added.size} added")
        if (removed.isNotEmpty()) parts.add("${removed.size} removed")
        if (modified.isNotEmpty()) parts.add("${modified.size} modified")
        
        return if (parts.isEmpty()) {
            "No changes"
        } else {
            parts.joinToString(", ")
        }
    }
}

/**
 * Exception thrown when registry operations fail
 */
class RegistryException(message: String, cause: Throwable? = null) : Exception(message, cause)
