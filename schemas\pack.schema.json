{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://orvynmmo.com/schemas/pack.schema.json", "title": "OrvynMMO Pack Configuration", "description": "Configuration schema for OrvynMMO content packs", "type": "object", "required": ["name", "version", "description"], "properties": {"name": {"type": "string", "pattern": "^[a-z0-9_-]+$", "description": "Pack identifier (lowercase, alphanumeric, hyphens, underscores)"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9.-]+)?$", "description": "Semantic version (e.g., 1.0.0, 1.0.0-alpha.1)"}, "description": {"type": "string", "maxLength": 500, "description": "Brief description of the pack"}, "author": {"type": "string", "description": "Pack author name"}, "license": {"type": "string", "description": "License identifier (SPDX format preferred)"}, "homepage": {"type": "string", "format": "uri", "description": "Pack homepage URL"}, "repository": {"type": "string", "format": "uri", "description": "Source repository URL"}, "dependencies": {"type": "object", "patternProperties": {"^[a-z0-9_-]+$": {"type": "string", "pattern": "^[~^]?\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9.-]+)?$"}}, "description": "Pack dependencies with version constraints"}, "orvynVersion": {"type": "string", "pattern": "^[~^]?\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9.-]+)?$", "description": "Required OrvynMMO version"}, "entrypoints": {"type": "object", "properties": {"skills": {"type": "string", "description": "Path to skills directory or file"}, "items": {"type": "string", "description": "Path to items directory or file"}, "classes": {"type": "string", "description": "Path to RPG classes directory or file"}, "drops": {"type": "string", "description": "Path to drop tables directory or file"}, "spawns": {"type": "string", "description": "Path to spawn configurations directory or file"}}, "description": "Entry points for different content types"}, "assets": {"type": "object", "properties": {"models": {"type": "string", "description": "Path to models directory"}, "textures": {"type": "string", "description": "Path to textures directory"}, "sounds": {"type": "string", "description": "Path to sounds directory"}}, "description": "Asset directories"}, "metadata": {"type": "object", "description": "Additional metadata"}}}