# OrvynMMO Development Server Startup Script (PowerShell)
# This script starts a Paper/Folia server with the OrvynMMO plugin for development

$ErrorActionPreference = "Stop"

$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$PluginDir = Split-Path -Parent $ScriptDir
$RootDir = Split-Path -Parent $PluginDir

Write-Host "Starting OrvynMMO development server..." -ForegroundColor Green

# Check if paper.jar exists
$PaperJar = Join-Path $ScriptDir "paper.jar"
if (-not (Test-Path $PaperJar)) {
    Write-Host "Error: paper.jar not found in $ScriptDir" -ForegroundColor Red
    Write-Host "Please download Paper/Folia and place it as 'paper.jar' in the run directory" -ForegroundColor Red
    exit 1
}

# Create plugins directory if it doesn't exist
$PluginsDir = Join-Path $ScriptDir "plugins"
if (-not (Test-Path $PluginsDir)) {
    New-Item -ItemType Directory -Path $PluginsDir | Out-Null
}

# Find the shaded jar
$TargetDir = Join-Path $PluginDir "target"
$ShadedJar = Get-ChildItem -Path $TargetDir -Filter "OrvynMMO-*-shaded.jar" | Select-Object -First 1

if (-not $ShadedJar) {
    Write-Host "Error: Shaded jar not found. Please run 'mvn package' first" -ForegroundColor Red
    exit 1
}

Write-Host "Found shaded jar: $($ShadedJar.FullName)" -ForegroundColor Yellow

# Copy the shaded jar to plugins directory
$DestinationJar = Join-Path $PluginsDir "OrvynMMO.jar"
Copy-Item -Path $ShadedJar.FullName -Destination $DestinationJar -Force
Write-Host "Copied plugin to plugins/OrvynMMO.jar" -ForegroundColor Green

# Create server.properties if it doesn't exist
$ServerProperties = Join-Path $ScriptDir "server.properties"
if (-not (Test-Path $ServerProperties)) {
    $PropertiesContent = @"
# OrvynMMO Development Server Configuration
server-port=25565
gamemode=creative
difficulty=peaceful
spawn-protection=0
max-players=10
online-mode=false
enable-command-block=true
motd=OrvynMMO Development Server
"@
    Set-Content -Path $ServerProperties -Value $PropertiesContent
    Write-Host "Created default server.properties" -ForegroundColor Green
}

# Create eula.txt if it doesn't exist
$EulaFile = Join-Path $ScriptDir "eula.txt"
if (-not (Test-Path $EulaFile)) {
    Set-Content -Path $EulaFile -Value "eula=true"
    Write-Host "Created eula.txt" -ForegroundColor Green
}

# Change to run directory
Set-Location $ScriptDir

# Start the server
Write-Host "Starting server..." -ForegroundColor Green
$JavaArgs = @(
    "-Dpaper.disableChannelWarning=true"
    "-Xms2G"
    "-Xmx4G"
    "-XX:+UseG1GC"
    "-XX:+ParallelRefProcEnabled"
    "-XX:MaxGCPauseMillis=200"
    "-XX:+UnlockExperimentalVMOptions"
    "-XX:+DisableExplicitGC"
    "-XX:+AlwaysPreTouch"
    "-XX:G1NewSizePercent=30"
    "-XX:G1MaxNewSizePercent=40"
    "-XX:G1HeapRegionSize=8M"
    "-XX:G1ReservePercent=20"
    "-XX:G1HeapWastePercent=5"
    "-XX:G1MixedGCCountTarget=4"
    "-XX:InitiatingHeapOccupancyPercent=15"
    "-XX:G1MixedGCLiveThresholdPercent=90"
    "-XX:G1RSetUpdatingPauseTimePercent=5"
    "-XX:SurvivorRatio=32"
    "-XX:+PerfDisableSharedMem"
    "-XX:MaxTenuringThreshold=1"
    "-Dusing.aikars.flags=https://mcflags.emc.gs"
    "-Daikars.new.flags=true"
    "-jar"
    "paper.jar"
    "nogui"
)

& java $JavaArgs
