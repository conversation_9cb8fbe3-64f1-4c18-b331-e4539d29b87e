package orvynmmo.items

import orvynmmo.OrvynMMOPlugin
import orvynmmo.core.registry.RegistryManager
import orvynmmo.skills.SkillManager

/**
 * Manages OrvynMMO items.
 */
class ItemManager(
    private val plugin: <PERSON>vynMMOPlugin,
    private val registryManager: RegistryManager,
    private val skillManager: SkillManager
) {
    
    /**
     * Initializes the item manager
     */
    fun initialize() {
        plugin.logInfo("Item manager initialized")
    }
    
    /**
     * Shuts down the item manager
     */
    fun shutdown() {
        plugin.logInfo("Item manager shut down")
    }
}
