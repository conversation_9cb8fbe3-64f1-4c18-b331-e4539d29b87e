import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import SkillEditor from './pages/SkillEditor'
import ItemEditor from './pages/ItemEditor'
import RPGEditor from './pages/RPGEditor'
import PackManager from './pages/PackManager'

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/skills" element={<SkillEditor />} />
          <Route path="/items" element={<ItemEditor />} />
          <Route path="/rpg" element={<RPGEditor />} />
          <Route path="/packs" element={<PackManager />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
