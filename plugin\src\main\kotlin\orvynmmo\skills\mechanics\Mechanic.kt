package orvynmmo.skills.mechanics

import orvynmmo.skills.SkillContext
import orvynmmo.skills.SkillResult

/**
 * Base interface for all skill mechanics.
 * 
 * Mechanics are the building blocks of skills that perform specific actions
 * like dealing damage, applying effects, or spawning particles.
 */
interface Mechanic {
    /**
     * Executes the mechanic with the given context
     */
    suspend fun execute(context: SkillContext): MechanicResult
    
    /**
     * Gets the display name of this mechanic
     */
    val displayName: String
    
    /**
     * Gets the type identifier of this mechanic
     */
    val type: String
}

/**
 * Result of a mechanic execution
 */
sealed class MechanicResult {
    data class Success(val message: String = "Mechanic executed successfully") : MechanicResult()
    data class Failure(val error: String) : MechanicResult()
    
    companion object {
        fun success(message: String = "Mechanic executed successfully") = Success(message)
        fun failure(error: String) = Failure(error)
    }
}

/**
 * Base class for simple mechanics
 */
abstract class SimpleMechanic : Mechanic {
    override suspend fun execute(context: SkillContext): MechanicResult {
        return try {
            executeSimple(context)
            MechanicResult.success()
        } catch (e: Exception) {
            MechanicResult.failure("Mechanic execution failed: ${e.message}")
        }
    }
    
    /**
     * Simple execution method for subclasses
     */
    protected abstract suspend fun executeSimple(context: SkillContext)
}

/**
 * Damage mechanic that deals damage to targets
 */
class DamageMechanic(
    private val amount: Double,
    private val damageType: String = "magic"
) : SimpleMechanic() {
    
    override val displayName: String = "Damage"
    override val type: String = "damage"
    
    override suspend fun executeSimple(context: SkillContext) {
        val target = context.target?.entity as? org.bukkit.entity.LivingEntity
        if (target != null) {
            // Apply damage (simplified implementation)
            target.damage(amount)
        }
    }
}

/**
 * Heal mechanic that restores health to targets
 */
class HealMechanic(
    private val amount: Double
) : SimpleMechanic() {
    
    override val displayName: String = "Heal"
    override val type: String = "heal"
    
    override suspend fun executeSimple(context: SkillContext) {
        val target = context.target?.entity as? org.bukkit.entity.LivingEntity
        if (target != null) {
            // Apply healing (simplified implementation)
            val newHealth = (target.health + amount).coerceAtMost(target.maxHealth)
            target.health = newHealth
        }
    }
}

/**
 * Particle mechanic that spawns visual effects
 */
class ParticleMechanic(
    private val particleType: String,
    private val count: Int = 10,
    private val spread: Double = 1.0
) : SimpleMechanic() {
    
    override val displayName: String = "Particle"
    override val type: String = "particle"
    
    override suspend fun executeSimple(context: SkillContext) {
        val location = context.getTargetLocation()
        
        // Spawn particles (simplified implementation)
        try {
            val particle = org.bukkit.Particle.valueOf(particleType.uppercase())
            location.world?.spawnParticle(particle, location, count, spread, spread, spread)
        } catch (e: IllegalArgumentException) {
            // Invalid particle type, ignore
        }
    }
}

/**
 * Sound mechanic that plays audio effects
 */
class SoundMechanic(
    private val sound: String,
    private val volume: Float = 1.0f,
    private val pitch: Float = 1.0f
) : SimpleMechanic() {
    
    override val displayName: String = "Sound"
    override val type: String = "sound"
    
    override suspend fun executeSimple(context: SkillContext) {
        val location = context.getTargetLocation()
        
        // Play sound (simplified implementation)
        try {
            val bukkitSound = org.bukkit.Sound.valueOf(sound.uppercase())
            location.world?.playSound(location, bukkitSound, volume, pitch)
        } catch (e: IllegalArgumentException) {
            // Invalid sound type, ignore
        }
    }
}
