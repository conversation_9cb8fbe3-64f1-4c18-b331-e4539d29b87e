<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.orvynmmo</groupId>
    <artifactId>orvynmmo</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>OrvynMMO</name>
    <description>Next-gen, Folia-safe, cinematic MMO framework for Paper/Purpur</description>
    <url>https://github.com/orvynmmo/orvynmmo</url>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Kotlin -->
        <kotlin.version>1.9.20</kotlin.version>
        <kotlinx.coroutines.version>1.7.3</kotlinx.coroutines.version>
        
        <!-- Paper -->
        <paper.version>1.21-R0.1-SNAPSHOT</paper.version>
        
        <!-- Adventure -->
        <adventure.version>4.14.0</adventure.version>
        
        <!-- Testing -->
        <junit.version>5.10.0</junit.version>
        <mockk.version>1.13.8</mockk.version>
        
        <!-- Plugins -->
        <maven.shade.version>3.5.1</maven.shade.version>
        <kotlin.maven.version>1.9.20</kotlin.maven.version>
        <ktlint.maven.version>3.0.0</ktlint.maven.version>
        <detekt.maven.version>1.23.3</detekt.maven.version>
        <surefire.version>3.2.2</surefire.version>
        <failsafe.version>3.2.2</failsafe.version>
    </properties>

    <modules>
        <module>plugin</module>
    </modules>

    <repositories>
        <repository>
            <id>papermc-repo</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
        <repository>
            <id>sonatype</id>
            <url>https://oss.sonatype.org/content/groups/public/</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
