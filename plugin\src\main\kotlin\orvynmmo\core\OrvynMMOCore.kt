package orvynmmo.core

import kotlinx.coroutines.CoroutineScope
import orvynmmo.OrvynMMOPlugin
import orvynmmo.core.registry.RegistryManager
import orvynmmo.obs.Profiler
import orvynmmo.region.RegionManager
import orvynmmo.skills.SkillManager
import orvynmmo.items.ItemManager
import orvynmmo.rpg.RPGManager
import orvynmmo.models.ModelManager

/**
 * Core system that coordinates all OrvynMMO subsystems.
 * 
 * Manages initialization, shutdown, and provides access to all major components.
 */
class OrvynMMOCore(
    private val plugin: OrvynMMOPlugin,
    private val registryManager: RegistryManager,
    private val regionManager: RegionManager,
    private val profiler: Profiler,
    private val scope: CoroutineScope
) {
    
    // Subsystem managers
    lateinit var skillManager: SkillManager
        private set
    
    lateinit var itemManager: ItemManager
        private set
    
    lateinit var rpgManager: RPGManager
        private set
    
    lateinit var modelManager: ModelManager
        private set
    
    private var isInitialized = false
    
    /**
     * Initializes all core systems
     */
    fun initialize() {
        if (isInitialized) {
            throw IllegalStateException("OrvynMMOCore is already initialized")
        }
        
        plugin.logInfo("Initializing core systems...")
        
        try {
            // Initialize managers in dependency order
            initializeModelManager()
            initializeSkillManager()
            initializeItemManager()
            initializeRPGManager()
            
            isInitialized = true
            plugin.logInfo("Core systems initialized successfully")
            
        } catch (e: Exception) {
            plugin.logError("Failed to initialize core systems", e)
            throw e
        }
    }
    
    /**
     * Shuts down all core systems
     */
    fun shutdown() {
        if (!isInitialized) {
            return
        }
        
        plugin.logInfo("Shutting down core systems...")
        
        try {
            // Shutdown in reverse order
            if (::rpgManager.isInitialized) {
                rpgManager.shutdown()
            }
            
            if (::itemManager.isInitialized) {
                itemManager.shutdown()
            }
            
            if (::skillManager.isInitialized) {
                skillManager.shutdown()
            }
            
            if (::modelManager.isInitialized) {
                modelManager.shutdown()
            }
            
            isInitialized = false
            plugin.logInfo("Core systems shut down successfully")
            
        } catch (e: Exception) {
            plugin.logError("Error during core system shutdown", e)
        }
    }
    
    private fun initializeModelManager() {
        plugin.logInfo("Initializing model manager...")
        modelManager = ModelManager(plugin, registryManager)
        modelManager.initialize()
    }
    
    private fun initializeSkillManager() {
        plugin.logInfo("Initializing skill manager...")
        skillManager = SkillManager(
            plugin = plugin,
            registryManager = registryManager,
            regionManager = regionManager,
            profiler = profiler,
            scope = scope
        )
        skillManager.initialize()
    }
    
    private fun initializeItemManager() {
        plugin.logInfo("Initializing item manager...")
        itemManager = ItemManager(
            plugin = plugin,
            registryManager = registryManager,
            skillManager = skillManager
        )
        itemManager.initialize()
    }
    
    private fun initializeRPGManager() {
        plugin.logInfo("Initializing RPG manager...")
        rpgManager = RPGManager(
            plugin = plugin,
            registryManager = registryManager,
            skillManager = skillManager
        )
        rpgManager.initialize()
    }
    
    /**
     * Gets the current initialization status
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * Gets core system statistics
     */
    fun getStats(): CoreStats {
        return CoreStats(
            isInitialized = isInitialized,
            registryStats = registryManager.getStats(),
            regionStats = regionManager.getStats(),
            profilerStats = profiler.getStats()
        )
    }
}

/**
 * Statistics about core system status
 */
data class CoreStats(
    val isInitialized: Boolean,
    val registryStats: orvynmmo.core.registry.RegistryStats,
    val regionStats: orvynmmo.region.RegionStats,
    val profilerStats: orvynmmo.obs.ProfilerStats
) {
    override fun toString(): String {
        return "CoreStats(initialized=$isInitialized, $registryStats, $regionStats, $profilerStats)"
    }
}
